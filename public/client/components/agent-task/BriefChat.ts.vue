<template>
  <div class="relative flex flex-col h-full bg-[#F5F5F5]">
    <!-- Confirm Modal -->
    <ConfirmModal
      v-model="showConfirmModal"
      title="Switching Briefs"
      message="Loading this brief will overwrite your current brief. Are you sure you want to continue?"
      confirm-text="Load Brief"
      cancel-text="Cancel"
      @confirm="confirmBriefLoad"
    />
    <div class="flex flex-1 overflow-hidden relative">
      <div class="flex flex-col flex-1 w-full h-full">
        <div
          ref="chatHistoryContainer"
          class="p-4 space-y-4 overflow-y-auto flex-1 w-full max-w-full chat-scrollbar"
          @scroll="handleScroll"
        >
          <div
            v-if="chatSegments.length === 0"
            class="flex flex-col items-center justify-center h-full pt-10"
          >
            <div class="flex flex-col space-y-4 w-full max-w-xl mx-auto py-6">
              <div
                class="bg-white rounded-xl border border-gray-200 shadow-md overflow-hidden p-6"
              >
                <div class="divide-y divide-gray-200">
                  <div
                    v-for="(prompt, index) in briefPrompts"
                    :key="index"
                    @click="handlePromptSelected(prompt.promptText)"
                    class="p-4 hover:bg-gray-100 transition-colors duration-150 ease-in-out cursor-pointer group"
                  >
                    <div class="flex items-start space-x-4">
                      <div class="flex-shrink-0 p-2 bg-purple-100 rounded-lg">
                        <component
                          :is="prompt.iconComponent"
                          class="h-6 w-6 text-purple-700"
                          aria-hidden="true"
                        />
                      </div>
                      <div class="flex-1 min-w-0">
                        <h3 class="font-medium text-[#202020] mb-1 truncate">
                          {{ prompt.title }}
                        </h3>
                        <p class="text-sm text-[#202020] line-clamp-2">
                          {{ prompt.description }}
                        </p>
                      </div>
                      <div
                        class="flex-shrink-0 text-purple-500 group-hover:translate-x-1 transition-transform duration-200 ease-in-out self-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            v-for="segment in chatSegments"
            :key="segment.id"
            :class="[
              'flex w-full',
              segment.sender === 'user' ? 'justify-end' : 'justify-start',
            ]"
          >
            <div
              v-if="segment.type === 'text' && segment.sender === 'user'"
              class="bg-purple-50 border border-purple-100 rounded-2xl p-4 text-gray-600 shadow-sm max-w-[80%]"
            >
              <div
                v-if="segment.content && containsFormatting(segment.content)"
                v-html="formatText(segment.content)"
              ></div>
              <template v-else>{{ segment.content }}</template>
            </div>

            <div
              v-if="
                segment.type === 'text' &&
                segment.sender === 'ai' &&
                segment.content &&
                segment.content.trim() !== ''
              "
              class="rounded-lg px-4 py-2 whitespace-pre-wrap bg-[#F5F5F5] text-[#202020] max-w-[80%]"
            >
              <div v-if="segment.isHtml" v-html="segment.content.trim()"></div>
              <div
                v-else-if="
                  segment.content && containsFormatting(segment.content.trim())
                "
                v-html="formatText(segment.content.trim())"
              ></div>
              <template v-else>{{ segment.content.trim() }}</template>
            </div>

            <div
              v-if="segment.type === 'brief_content'"
              class="flex flex-col w-full max-w-[80%] p-4 rounded-lg bg-white border border-purple-200 shadow-sm"
            >
              <div class="flex items-center justify-between mb-3">
                <span class="font-semibold text-base text-[#202020]"
                  >Campaign Brief</span
                >
                <div class="flex-shrink-0">
                  <div
                    class="flex items-center justify-center p-2 rounded-md border border-gray-200 bg-gray-50 h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-[#5A16C9]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M9.75 17.25v-.001M9.75 14.25v-.001M9.75 11.25v-.001M12 17.25v-.001M12 14.25v-.001M12 11.25v-.001M14.25 17.25v-.001M14.25 14.25v-.001M14.25 11.25v-.001M4.5 20.25h15A2.25 2.25 0 0021.75 18V6A2.25 2.25 0 0019.5 3.75H4.5A2.25 2.25 0 002.25 6v12A2.25 2.25 0 004.5 20.25z"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="text-sm text-gray-700 whitespace-pre-wrap">
                {{ decodeBriefText(segment.content || '') }}
              </div>
            </div>

            <div
              v-if="segment.type === 'brief_placeholder'"
              class="rounded-lg px-4 py-2 bg-purple-100 text-purple-700 shadow-sm max-w-[80%] italic"
            >
              Generating brief...
            </div>

            <div
              v-if="segment.type === 'brief_error'"
              class="w-full max-w-[80%]"
            >
              <BriefParseError
                errorType="brief"
                :errorMessage="
                  segment.errorMessage || 'Failed to parse brief data'
                "
                :errorDetails="segment.errorDetails || ''"
                @retry="handleErrorRetry(segment, $event)"
              />
            </div>

            <div
              v-if="segment.type === 'email_error'"
              class="w-full max-w-[80%]"
            >
              <BriefParseError
                errorType="email"
                :errorMessage="
                  segment.errorMessage || 'Failed to parse email data'
                "
                :errorDetails="segment.errorDetails || ''"
                @retry="handleErrorRetry(segment, $event)"
              />
            </div>

            <div
              v-if="segment.type === 'historic_brief'"
              class="flex items-center justify-between w-full max-w-[80%] p-4 rounded-lg bg-white border border-purple-200 shadow-sm cursor-pointer transition-colors duration-200 ease-in-out hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-[#9254F7] focus:ring-opacity-75"
              @click="loadHistoricBrief(segment)"
              :data-segment-id="segment.id"
            >
              <div class="flex flex-col items-start mr-4">
                <span class="font-semibold text-base mb-1 text-[#202020]"
                  >Campaign Brief</span
                >
                <span class="text-sm text-gray-400"
                  >Click to load into editor</span
                >
                <span class="text-xs text-purple-600 mt-1 brief-status">
                  {{
                    segment.timestamp &&
                    new Date().getTime() -
                      new Date(segment.timestamp).getTime() <
                      60000
                      ? 'Generated just now'
                      : 'From historic chat'
                  }}
                </span>
              </div>

              <div class="flex-shrink-0">
                <div
                  class="flex items-center justify-center p-2 rounded-md border border-gray-200 bg-gray-50 h-10 w-10"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-[#5A16C9]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M9.75 17.25v-.001M9.75 14.25v-.001M9.75 11.25v-.001M12 17.25v-.001M12 14.25v-.001M12 11.25v-.001M14.25 17.25v-.001M14.25 14.25v-.001M14.25 11.25v-.001M4.5 20.25h15A2.25 2.25 0 0021.75 18V6A2.25 2.25 0 0019.5 3.75H4.5A2.25 2.25 0 002.25 6v12A2.25 2.25 0 004.5 20.25z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <div
              v-if="segment.type === 'email_placeholder'"
              class="rounded-lg px-4 py-2 bg-purple-100 text-purple-700 shadow-sm max-w-[80%] italic"
            >
              Generating email design...
            </div>

            <div
              v-if="segment.type === 'historic_email'"
              class="flex items-center justify-between w-full max-w-[80%] p-4 rounded-lg bg-white border border-purple-200 shadow-sm cursor-pointer transition-colors duration-200 ease-in-out hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-[#9254F7] focus:ring-opacity-75"
              @click="loadHistoricEmail(segment)"
              :data-segment-id="segment.id"
            >
              <div class="flex flex-col items-start mr-4">
                <span class="font-semibold text-base mb-1 text-[#202020]"
                  >Email Design</span
                >
                <span class="text-sm text-gray-400"
                  >Click to load into editor</span
                >
                <span class="text-xs text-purple-600 mt-1 email-status">
                  {{
                    segment.timestamp &&
                    new Date().getTime() -
                      new Date(segment.timestamp).getTime() <
                      60000
                      ? 'Generated just now'
                      : 'From historic chat'
                  }}
                </span>
              </div>

              <div class="flex-shrink-0">
                <div
                  class="flex items-center justify-center p-2 rounded-md border border-gray-200 bg-gray-50 h-10 w-10"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-[#5A16C9]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Image Display Component (Thumbnail Style) -->
            <!-- Single Image Renderer Component -->
            <SingleImageRenderer
              v-if="segment.type === 'image' && segment.imageUrl"
              :image-id="segment.id"
              :image-url="segment.imageUrl"
              :image-name="segment.content"
              :initial-load-state="segment.imageLoaded || false"
              @image-loaded="handleImageLoad"
              @image-error="handleImageError"
            />

            <!-- Multi-Image Display Component (Grid Style) -->
            <!-- Multi Image Renderer Component -->
            <MultiImageRenderer
              v-if="
                segment.type === 'multiimage' &&
                segment.images &&
                segment.images.length > 0
              "
              :image-id="segment.id"
              :images="segment.images"
              @multi-image-loaded="handleMultiImageLoad"
              @multi-image-error="handleMultiImageError"
            />

            <div
              v-if="segment.type === 'memory' && segment.memory"
              class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm max-w-md"
            >
              <div class="text-sm font-semibold text-gray-800">
                {{ segment.memory.category }}
              </div>
              <div class="text-sm text-gray-600 whitespace-pre-wrap">
                {{ segment.memory.info }}
              </div>
            </div>
          </div>

          <div
            v-if="isWaitingForAI"
            class="flex justify-start items-center space-x-2"
          >
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
              style="animation-delay: -0.3s"
            ></div>
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
              style="animation-delay: -0.15s"
            ></div>
            <div
              class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
            ></div>
            <span class="text-sm text-gray-500">Thinking...</span>
          </div>
        </div>

        <div
          class="ml-4 mr-4 p-4 px-4 border-t border-gray-200 shadow-md bg-white max-w-full rounded-xl relative"
          :class="{
            'mb-auto rounded-xl': chatSegments.length === 0,
          }"
        >
          <!-- Down Arrow Button -->
          <button
            v-if="!isAtBottom && chatSegments.length > 0"
            @click="scrollToBottom"
            class="absolute left-1/2 transform -translate-x-1/2 -top-6 p-1.5 rounded-full bg-purple-100 hover:bg-purple-200 transition-colors duration-200 text-purple-700 focus:outline-none z-10 shadow-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <!-- Quick Action Buttons -->
          <div class="flex flex-wrap items-center justify-start gap-2 mb-3">
            <div
              v-for="(action, index) in quickActions"
              :key="index"
              class="relative group"
            >
              <button
                @click="handleQuickAction(action.promptText)"
                class="flex items-center px-4 py-1.5 text-xs font-medium rounded-full bg-white border border-gray-200 hover:bg-gray-50 hover:border-purple-200 transition-colors duration-150 shadow-sm"
                :class="{'opacity-70': isWaitingForAI}"
              >
                <span class="text-[#5A16C9]">{{ action.title }}</span>
              </button>
              <!-- Hover tooltip -->
              <div
                class="absolute left-0 bottom-full mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10"
              >
                {{ action.description }}
              </div>
            </div>
          </div>

          <div
            class="flex items-center bg-white rounded-lg p-3 border border-gray-200 mb-3 w-full"
          >
            <textarea
              ref="messageTextarea"
              v-model="newMessage"
              :placeholder="isWaitingForAI ? 'Continue typing your next message while AI generates...' : 'Message your AI campaign assistant...'"
              class="flex-1 bg-transparent border-none focus:ring-0 focus:outline-none px-1 text-gray-600 placeholder-gray-400 w-full resize-none overflow-hidden leading-normal"
              :class="{'opacity-70': isWaitingForAI}"
              rows="1"
              @input="resizeMessageTextarea"
              @keydown.enter="handleEnterKey"
              style="min-height: 24px; max-height: 96px; line-height: 1.5"
              @click="(e: MouseEvent) => { const target = e.target as HTMLTextAreaElement; if (target) target.focus(); }"
            ></textarea>
            <button
              @click="() => sendMessage()"
              :disabled="!newMessage.trim() || isWaitingForAI || !canSendMessage"
              class="ml-3 p-1.5 rounded-md bg-[#E9D5FF] text-[#5A16C9] transition-colors duration-200 relative"
              :class="
                newMessage.trim() && !isWaitingForAI && canSendMessage
                  ? 'hover:bg-[#D8B4FE]'
                  : 'bg-purple-100 text-purple-300 cursor-not-allowed'
              "
              :title="isWaitingForAI ? 'AI is generating content. You can keep typing but can\'t send until it completes.' : ''"
            >
              <span v-if="isWaitingForAI" class="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-green-400 animate-pulse" title="AI is generating content. You can still type but cannot send yet."></span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height="20px"
                viewBox="0 -960 960 960"
                width="20px"
                fill="currentColor"
              >
                <path
                  d="M440-160v-487L216-423l-56-57 320-320 320 320-56 57-224-224v487h-80Z"
                />
              </svg>
            </button>
          </div>
        </div>
        <div
          class="text-center text-xs text-gray-500 py-1 w-full max-w-full mb-3"
        >
      Raleon AI can make mistakes, always double check before sending.
        </div>
      </div>
    </div>
    <MessageQuotaModal :show="showQuotaModal" @close="showQuotaModal = false" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  nextTick,
  ref,
  onMounted,
  watch,
  computed,
} from '@vue/runtime-core';
import {jsonrepair} from 'jsonrepair';
import {
  startConversation,
  postMessage,
  getConversation,
} from '../../services/chatService'; // Added getConversation import
import {customerIOTrackEvent} from '../../services/customerio.js';
import {
  ChatBubbleLeftRightIcon,
  LightBulbIcon,
  SparklesIcon,
  DocumentTextIcon,
} from '@heroicons/vue/24/outline';
import {
  MessageParserService,
  ParsedSegment,
  TextSegment,
  ToolStartSegment,
  ToolContentSegment,
  ToolEndSegment,
} from '../../services/messageParserService';
import {messageArtifactService, ChatMessageSegment} from '../../services/messageArtifactService';
import {TextFormattingService} from '../../services/textFormattingService';
import BriefParseError from './BriefParseError.vue';
import SingleImageRenderer from './SingleImageRenderer.vue';
import MultiImageRenderer from './MultiImageRenderer.vue';
import ConfirmModal from '../common/ConfirmModal.vue';
import MessageQuotaModal from '../MessageQuotaModal.ts.vue';
import {fetchMessageQuota, useQuotaService} from '../../services/messageQuotaService';
import * as Utils from '../../../client-old/utils/Utils';

export default defineComponent({
  name: 'BriefChat',
  components: {
    BriefParseError,
    SingleImageRenderer,
    MultiImageRenderer,
    ConfirmModal,
    MessageQuotaModal,
  },
  props: {
    currentBriefText: {
      type: String,
      required: true,
    },
    campaignId: {
      type: String,
      required: true,
    },
    conversationId: {
      type: [String, Number],
      required: false,
      default: null,
    },
    forceReload: {
      type: Boolean,
      default: false,
    },
  },
  emits: [
    'update:brief',
    'update:raw-brief-stream',
    'brief-stream-complete',
    'auto-save-brief',
    'update:email',
    'update:raw-email-stream',
    'email-stream-complete',
    'auto-save-email',
    'generate-email-from-components',
  ],
  setup(props, {emit}) {
    const chatHistoryContainer = ref<HTMLElement | null>(null);
    const messageTextarea = ref<HTMLTextAreaElement | null>(null);
    const newMessage = ref('');
    const chatSegments = ref<ChatMessageSegment[]>([]);
    const isWaitingForAI = ref(false);
    const segmentIdCounter = ref(0);
    const conversationId = ref<string | null>(null);
    const messageParserService = new MessageParserService();
    const currentBriefPlaceholderId = ref<string | null>(null);
    const currentEmailPlaceholderId = ref<string | null>(null);
    const streamingBriefContent = ref('');
    const streamingEmailContent = ref('');
    // Raw streaming JSON for brief and email
    const rawStreamingBrief = ref('');
    const rawStreamingEmail = ref('');
    // Track if the chat is scrolled to the bottom
    const isAtBottom = ref(true);

    // Confirm modal state
    const showConfirmModal = ref(false);
    const pendingBriefSegment = ref<ChatMessageSegment | null>(null);

    const showQuotaModal = ref(false);
    
    // Use the reactive quota service
    const quotaService = useQuotaService();

    // Make TextFormattingService available in the template
    const formatText = TextFormattingService.formatText;
    const containsFormatting = TextFormattingService.containsFormatting;

    // Quick action buttons with image icons
    const quickActions = [
      {
        title: 'Generate email',
        description: 'Generate an email based on my prompt.',
        promptText: 'Generate an email based on my brief.',
        imageUrl: null,
      },
      {
        title: 'Generate Brief',
        description: 'Generate a new brief for me.',
        promptText: 'Generate a new brief for me.',
        imageUrl: null,
      },
      {
        title: 'Explain the Brief',
        description:
          'Tell me what data you used and why this brief is good, and what improvements we should consider.',
        promptText:
          'Tell me what data you used and why this brief is good, and what improvements we should consider.',
        imageUrl: null,
      },
      {
        title: 'Show my Images',
        description: 'Show me the images I am currently using in my email.',
        promptText: 'Show me the images I am currently using in my email.',
        imageUrl: null,
      },
    ];

    // Brief prompts for quick actions
    const briefPrompts = [
      {
        title: 'Tell me how you came up with this brief',
        description:
          'Understand the reasoning and thought process behind this brief.',
        promptText:
          'Tell me how you came up with this brief and what your thought process was.',
        iconComponent: ChatBubbleLeftRightIcon,
      },
      {
        title: 'Why do you think this brief will work',
        description:
          'Learn why this brief is effective for your target audience.',
        promptText: 'Why do you think this brief will work for my audience?',
        iconComponent: LightBulbIcon,
      },
      {
        title: 'Generate an Email from this brief',
        description:
          'Generate an email based on this brief utilizing my email components.',
        promptText: 'Generate an email from this brief.',
        iconComponent: SparklesIcon,
      },
      {
        title: 'Give me an entirely new brief',
        description:
          'Generate a completely different approach for this campaign.',
        promptText: 'Give me an entirely new brief with a different approach.',
        iconComponent: DocumentTextIcon,
      },
    ];

    // --- Methods ---

    const generateUniqueId = (): string => {
      segmentIdCounter.value++;
      return `briefchat-seg-${Date.now()}-${segmentIdCounter.value}`;
    };

    const scrollToBottom = () => {
      nextTick(() => {
        if (chatHistoryContainer.value) {
          chatHistoryContainer.value.scrollTop =
            chatHistoryContainer.value.scrollHeight;
          isAtBottom.value = true;
        }
      });
    };

    // Check if the chat is scrolled to the bottom
    const handleScroll = () => {
      if (chatHistoryContainer.value) {
        const {scrollTop, scrollHeight, clientHeight} =
          chatHistoryContainer.value;
        // Consider "at bottom" if within 20px of the bottom
        const wasAtBottom = isAtBottom.value;
        isAtBottom.value = scrollHeight - scrollTop - clientHeight < 20;

        // If we just detected that we're no longer at the bottom during streaming,
        // force a re-render to show the down arrow
        if (wasAtBottom && !isAtBottom.value) {
          console.log('Content went off screen, showing down arrow');
          // Force Vue to update the DOM
          nextTick();
        }
      }
    };

    const handleApiStream = async (
      streamPromise: Promise<ReadableStream | null>,
    ) => {
      const stream = await streamPromise;
      if (!stream) {
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'brief_error',
          sender: 'ai',
          errorMessage: 'Connection Error',
          errorDetails:
            'Could not connect to the server. Please check your internet connection and try again.',
          timestamp: new Date(),
        });
        isWaitingForAI.value = false;
        scrollToBottom();
        return;
      }

      // Reset streaming state
      streamingBriefContent.value = '';
      rawStreamingBrief.value = '';
      streamingEmailContent.value = '';
      rawStreamingEmail.value = '';
      console.log('Stream started, reset streaming content buffers');

      const reader = stream.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const {value, done} = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, {stream: true});

          // Special check for brief and email end tags
          if (currentBriefPlaceholderId.value && chunk.includes('</brief>')) {
            console.log('Found </brief> tag in chunk:', chunk);
          }
          if (currentEmailPlaceholderId.value && chunk.includes('</email>')) {
            console.log('Found </email> tag in chunk:', chunk);
          }

          buffer += chunk;
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() && line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(5));
                handleStreamingEvent(data);
              } catch (e) {
                console.error('Failed to parse SSE data:', line, e);
              }
            }
          }
        }
      } catch (error) {
        console.error('Error reading stream:', error);
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'brief_error',
          sender: 'ai',
          errorMessage: 'Stream Error',
          errorDetails: 'Error reading response stream. Please try again.',
          timestamp: new Date(),
        });
      } finally {
        // Check if we have any remaining buffer to process
        if (buffer.trim()) {
          console.log('Processing final buffer:', buffer);

          // Special check for brief and email end tags in final buffer
          if (currentBriefPlaceholderId.value && buffer.includes('</brief>')) {
            console.log('Found </brief> tag in final buffer');
          }
          if (currentEmailPlaceholderId.value && buffer.includes('</email>')) {
            console.log('Found </email> tag in final buffer');
          }

          if (buffer.startsWith('data: ')) {
            try {
              const data = JSON.parse(buffer.slice(5));
              handleStreamingEvent(data);
            } catch (e) {
              console.error('Failed to parse final SSE data:', buffer, e);
            }
          }
        }

        // Special check for unclosed brief tag
        if (
          currentBriefPlaceholderId.value &&
          streamingBriefContent.value &&
          !streamingBriefContent.value.includes('</brief>')
        ) {
          console.log(
            'Stream ended but no </brief> tag found. Adding closing tag.',
          );
          streamingBriefContent.value += '</brief>';
          rawStreamingBrief.value += '</brief>';
        }

        // Special check for unclosed email tag
        if (
          currentEmailPlaceholderId.value &&
          streamingEmailContent.value &&
          !streamingEmailContent.value.includes('</email>')
        ) {
          console.log(
            'Stream ended but no </email> tag found. Adding closing tag.',
          );
          streamingEmailContent.value += '</email>';
          rawStreamingEmail.value += '</email>';
        }

        reader.releaseLock();
        messageParserService.reset(); // Reset parser state
        // Reset brief-specific state if needed, though tool_end should handle it
        // currentBriefPlaceholderId.value = null;
        // streamingBriefContent.value = '';
        isWaitingForAI.value = false;
        console.log('Stream finished');
        // Never auto-scroll at the end of streaming
        // Just make sure the down arrow is shown if needed
        nextTick(() => {
          if (chatHistoryContainer.value) {
            const {scrollTop, scrollHeight, clientHeight} =
              chatHistoryContainer.value;
            if (
              scrollHeight > clientHeight &&
              scrollHeight - scrollTop - clientHeight > 20
            ) {
              isAtBottom.value = false;
              console.log(
                'End of streaming: content exceeds visible area, showing down arrow',
              );
            }
          }
        });
      }
    };

    const handleStreamingEvent = (data: any) => {
      if (data.conversationId && !conversationId.value) {
        conversationId.value = data.conversationId;
        console.log('BriefChat Conversation ID set:', conversationId.value);
      }

      if (data.content) {
        // Check if the content contains a brief tag directly
        if (
          data.content.includes('<brief>') &&
          data.content.includes('</brief>')
        ) {
          console.log(
            'Found complete brief tags in streaming event, ensuring proper processing',
          );
        }

        const parsedSegments = messageParserService.processChunk(data.content);
        updateChatSegments(parsedSegments);
      }

      // Handle status if needed

      // Don't auto-scroll during streaming
      // But check if we need to show the down arrow
      nextTick(() => {
        if (chatHistoryContainer.value) {
          const {scrollTop, scrollHeight, clientHeight} =
            chatHistoryContainer.value;
          // If content has gone beyond the visible area, update isAtBottom
          if (
            scrollHeight > clientHeight &&
            scrollHeight - scrollTop - clientHeight > 20
          ) {
            isAtBottom.value = false;
            //console.log('Content exceeds visible area during streaming, showing down arrow');
          }
        }
      });
    };

    const updateChatSegments = (segments: ParsedSegment[]) => {
      let firstContentSegmentProcessed = false;

      // Check if we need to show the down arrow before processing segments
      nextTick(() => {
        handleScroll();
      });

      for (const segment of segments) {
        // Turn off thinking indicator on first content
        if (
          isWaitingForAI.value &&
          !firstContentSegmentProcessed &&
          (segment.type === 'text' || segment.type === 'tool_start')
        ) {
          isWaitingForAI.value = false;
          firstContentSegmentProcessed = true;
        }

        const lastSegment =
          chatSegments.value.length > 0
            ? chatSegments.value[chatSegments.value.length - 1]
            : null;

        switch (segment.type) {
          case 'text':
            const textSegment = segment as TextSegment;

            // Check if this text segment contains a <multiimage> tag
            if (
              textSegment.content.includes('<multiimage>') &&
              textSegment.content.includes('</multiimage>')
            ) {
              console.log(
                'Found <multiimage> tags in text segment, processing as multiimage',
              );

              try {
                // Find all multiimage tags in the message
                const multiimageRegex =
                  /<multiimage>\s*([\s\S]*?)\s*<\/multiimage>/gim;
                let fullContent = textSegment.content;
                let lastEndIndex = 0;
                let match: RegExpExecArray | null;
                let foundMultiimages = false;

                // Process each multiimage tag found in the message
                while ((match = multiimageRegex.exec(fullContent)) !== null) {
                  foundMultiimages = true;
                  const multiimageTagStart = match.index;
                  const multiimageTagEnd = multiimageTagStart + match[0].length;
                  const multiimageContent = match[1].trim();

                  // Add text between the last multiimage and this one
                  if (multiimageTagStart > lastEndIndex) {
                    const textBetween = fullContent
                      .substring(lastEndIndex, multiimageTagStart)
                      .trim();
                    if (textBetween) {
                      chatSegments.value.push({
                        id: generateUniqueId(),
                        type: 'text',
                        sender: 'ai',
                        content: textBetween,
                        timestamp: new Date(),
                      });
                    }
                  }

                  // Parse the multiimage JSON content
                  try {
                    const imagesData = JSON.parse(multiimageContent);
                    if (Array.isArray(imagesData) && imagesData.length > 0) {
                      // Add the multiimage segment
                      chatSegments.value.push({
                        id: generateUniqueId(),
                        type: 'multiimage',
                        sender: 'ai',
                        timestamp: new Date(),
                        images: imagesData.map(img => ({
                          url: img.url,
                          name: img.name || '',
                          loaded: false, // Start with images not loaded
                        })),
                      });
                    } else {
                      console.error(
                        'Invalid multiimage data format:',
                        imagesData,
                      );
                      // Add error message as text
                      chatSegments.value.push({
                        id: generateUniqueId(),
                        type: 'text',
                        sender: 'ai',
                        content: 'Error: Invalid multiimage data format.',
                        timestamp: new Date(),
                      });
                    }
                  } catch (parseError) {
                    console.error(
                      'Failed to parse multiimage JSON:',
                      parseError,
                    );
                    // Add error message as text
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: 'Error: Failed to parse multiimage data.',
                      timestamp: new Date(),
                    });
                  }

                  // Update the last end index
                  lastEndIndex = multiimageTagEnd;
                }

                // Add any remaining text after the last multiimage
                if (foundMultiimages && lastEndIndex < fullContent.length) {
                  const textAfter = fullContent.substring(lastEndIndex).trim();
                  if (textAfter) {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: textAfter,
                      timestamp: new Date(),
                    });
                  }
                }

                // If no multiimages were found (shouldn't happen since we checked for tags)
                if (!foundMultiimages) {
                  if (
                    lastSegment &&
                    lastSegment.type === 'text' &&
                    lastSegment.sender === 'ai'
                  ) {
                    lastSegment.content += textSegment.content;
                  } else {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: textSegment.content,
                      timestamp: new Date(),
                    });
                  }
                }
              } catch (error) {
                console.error(
                  'Error processing multiimage in text segment:',
                  error,
                );
                // Fallback: add as regular text
                if (
                  lastSegment &&
                  lastSegment.type === 'text' &&
                  lastSegment.sender === 'ai'
                ) {
                  lastSegment.content += textSegment.content;
                } else {
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'text',
                    sender: 'ai',
                    content: textSegment.content,
                    timestamp: new Date(),
                  });
                }
              }
            }
            // Check if this text segment contains an <image> tag
            else if (
              textSegment.content.includes('<image>') &&
              textSegment.content.includes('</image>')
            ) {
              console.log(
                'Found <image> tags in text segment, processing as image',
              );

              try {
                // Find all image tags in the message
                const imageRegex = /<image>\s*([\s\S]*?)\s*<\/image>/gim;
                let fullContent = textSegment.content;
                let lastEndIndex = 0;
                let match: RegExpExecArray | null;
                let foundImages = false;

                // Process each image tag found in the message
                while ((match = imageRegex.exec(fullContent)) !== null) {
                  foundImages = true;
                  const imageTagStart = match.index;
                  const imageTagEnd = imageTagStart + match[0].length;
                  const imageContent = match[1].trim();

                  // Try to parse as JSON first (new format)
                  let imageUrl = '';
                  let imageName = 'Image';

                  try {
                    // Check if the content is JSON
                    if (
                      imageContent.startsWith('{') &&
                      imageContent.endsWith('}')
                    ) {
                      const imageData = JSON.parse(imageContent);
                      imageUrl = imageData.url || '';
                      imageName = imageData.name || 'Image';
                      console.log(
                        `Parsed JSON image data: name=${imageName}, url=${imageUrl}`,
                      );
                    } else {
                      // Legacy format - content is just the URL
                      imageUrl = imageContent;
                      // Try to extract a simple name from the URL
                      const urlParts = imageUrl.split('/');
                      const fileName = urlParts[urlParts.length - 1];
                      if (fileName.includes('generated-')) {
                        imageName = 'Generated Image';
                      }
                      console.log(
                        `Using legacy image format with URL: ${imageUrl}`,
                      );
                    }
                  } catch (jsonError) {
                    // If JSON parsing fails, treat the content as a direct URL (legacy format)
                    console.warn(
                      'Failed to parse image JSON, using as direct URL:',
                      jsonError,
                    );
                    imageUrl = imageContent;
                  }

                  console.log(
                    `Found image tag at position ${imageTagStart}-${imageTagEnd} with name: ${imageName}`,
                  );

                  // Add text between the last image and this one
                  if (imageTagStart > lastEndIndex) {
                    const textBetween = fullContent
                      .substring(lastEndIndex, imageTagStart)
                      .trim();
                    if (textBetween) {
                      chatSegments.value.push({
                        id: generateUniqueId(),
                        type: 'text',
                        sender: 'ai',
                        content: textBetween,
                        timestamp: new Date(),
                      });
                    }
                  }

                  // Add the image segment with name and URL
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'image',
                    sender: 'ai',
                    imageUrl: imageUrl,
                    content: imageName, // Store the image name in the content field
                    timestamp: new Date(),
                    imageLoaded: false, // Start with image not loaded
                  });

                  // Update the last end index
                  lastEndIndex = imageTagEnd;
                }

                // Add any remaining text after the last image
                if (foundImages && lastEndIndex < fullContent.length) {
                  const textAfter = fullContent.substring(lastEndIndex).trim();
                  if (textAfter) {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: textAfter,
                      timestamp: new Date(),
                    });
                  }
                }

                // If no images were found (shouldn't happen since we checked for tags)
                if (!foundImages) {
                  if (
                    lastSegment &&
                    lastSegment.type === 'text' &&
                    lastSegment.sender === 'ai'
                  ) {
                    lastSegment.content += textSegment.content;
                  } else {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: textSegment.content,
                      timestamp: new Date(),
                    });
                  }
                }
              } catch (error) {
                console.error('Error processing image in text segment:', error);
                // Fallback: add as regular text
                if (
                  lastSegment &&
                  lastSegment.type === 'text' &&
                  lastSegment.sender === 'ai'
                ) {
                  lastSegment.content += textSegment.content;
                } else {
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'text',
                    sender: 'ai',
                    content: textSegment.content,
                    timestamp: new Date(),
                  });
                }
              }
            }
            // Check if this text segment contains a <brief> tag
            else if (
              textSegment.content.includes('<brief>') &&
              textSegment.content.includes('</brief>')
            ) {
              console.log(
                'Found <brief> tags in text segment, processing as historic brief',
              );

              // Extract brief content using regex
              const briefMatches = textSegment.content.match(
                /<brief>\s*([\s\S]*?)\s*<\/brief>/im,
              );

              if (briefMatches && briefMatches[1]) {
                try {
                  // Extract text before the brief tag
                  const briefTagStart = textSegment.content.indexOf('<brief>');
                  const briefTagEnd =
                    textSegment.content.indexOf('</brief>') + '</brief>'.length;

                  const textBefore = textSegment.content
                    .substring(0, briefTagStart)
                    .trim();
                  if (textBefore) {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: textBefore,
                      timestamp: new Date(),
                    });
                  }

                  // Parse the brief content
                  const briefContent = briefMatches[1].trim();
                  let parsedBrief: {
                    subjectLine: string;
                    previewText: string;
                    briefText: string;
                  };

                  try {
                    // First attempt: direct parsing
                    parsedBrief = JSON.parse(briefContent);
                  } catch (parseError) {
                    console.error('Initial JSON parse failed:', parseError);

                    try {
                      // Second attempt: try with jsonrepair
                      const repairedJson = jsonrepair(briefContent);
                      parsedBrief = JSON.parse(repairedJson);
                    } catch (repairError) {
                      console.error(
                        'JSON repair and parse failed:',
                        repairError,
                      );
                      // Create a default brief object
                      parsedBrief = {
                        subjectLine: 'Default Subject',
                        previewText: 'Default Preview',
                        briefText: 'Default Brief Content',
                      };
                    }
                  }

                  // Add the historic brief card segment
                  const briefSegmentId = generateUniqueId();
                  chatSegments.value.push({
                    id: briefSegmentId,
                    type: 'historic_brief', // Use historic_brief type for briefs in text segments
                    sender: 'ai',
                    content: 'Campaign Brief',
                    timestamp: new Date(),
                    briefData: parsedBrief,
                    rawContent: briefContent,
                    isGenerating: false,
                    artifactType: 'brief',
                  });

                  console.log(
                    'Added historic_brief segment from text segment with ID:',
                    briefSegmentId,
                  );

                  // Extract text after the brief tag
                  const textAfter = textSegment.content
                    .substring(briefTagEnd)
                    .trim();
                  if (textAfter) {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: textAfter,
                      timestamp: new Date(),
                    });
                  }
                } catch (error) {
                  console.error(
                    'Error processing brief in text segment:',
                    error,
                  );
                  // Fallback: add as regular text
                  if (
                    lastSegment &&
                    lastSegment.type === 'text' &&
                    lastSegment.sender === 'ai'
                  ) {
                    lastSegment.content += textSegment.content;
                  } else {
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: textSegment.content,
                      timestamp: new Date(),
                    });
                  }
                }
              } else {
                // No valid brief content found
                if (
                  lastSegment &&
                  lastSegment.type === 'text' &&
                  lastSegment.sender === 'ai'
                ) {
                  lastSegment.content += textSegment.content;
                } else {
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'text',
                    sender: 'ai',
                    content: textSegment.content,
                    timestamp: new Date(),
                  });
                }
              }
            }
            // Check if this text segment contains a brief content section with [Brief content] format
            else if (
              textSegment.content.includes(
                "I'll provide an alternative main body content section",
              ) &&
              textSegment.content.includes('[Brief content]')
            ) {
              // Extract the brief content
              const briefContentMatch = textSegment.content.match(
                /\[Brief content\]([\s\S]*?)(?:Would you like|Let me know|$)/i,
              );
              if (briefContentMatch && briefContentMatch[1]) {
                const briefContent = briefContentMatch[1].trim();

                // Extract text before the brief content
                const textBefore = textSegment.content
                  .substring(0, textSegment.content.indexOf('[Brief content]'))
                  .trim();
                if (textBefore) {
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'text',
                    sender: 'ai',
                    content: textBefore,
                    timestamp: new Date(),
                  });
                }

                // Add the brief content as a special segment
                chatSegments.value.push({
                  id: generateUniqueId(),
                  type: 'brief_content',
                  sender: 'ai',
                  content: briefContent,
                  timestamp: new Date(),
                });

                // Extract text after the brief content
                const afterMatch = textSegment.content.match(
                  /(?:Would you like|Let me know|$)([\s\S]*?)$/i,
                );
                if (afterMatch && afterMatch[0]) {
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'text',
                    sender: 'ai',
                    content: afterMatch[0].trim(),
                    timestamp: new Date(),
                  });
                }
              } else {
                // Fallback if we can't extract the brief content
                if (
                  lastSegment &&
                  lastSegment.type === 'text' &&
                  lastSegment.sender === 'ai'
                ) {
                  lastSegment.content += textSegment.content;
                } else {
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'text',
                    sender: 'ai',
                    content: textSegment.content,
                    timestamp: new Date(),
                  });
                }
              }
            } else if (
              lastSegment &&
              lastSegment.type === 'text' &&
              lastSegment.sender === 'ai'
            ) {
              lastSegment.content += textSegment.content;
            } else {
              chatSegments.value.push({
                id: generateUniqueId(),
                type: 'text',
                sender: 'ai',
                content: textSegment.content,
                timestamp: new Date(),
              });
            }
            break;

          case 'tool_start':
            const toolStart = segment as ToolStartSegment;
            if (toolStart.tag === 'brief') {
              const placeholderId = generateUniqueId();
              chatSegments.value.push({
                id: placeholderId,
                type: 'brief_placeholder',
                sender: 'ai',
                isGenerating: true,
                timestamp: new Date(),
              });
              currentBriefPlaceholderId.value = placeholderId;
              streamingBriefContent.value = ''; // Clear any previous streaming content
            } else if (toolStart.tag === 'email') {
              const placeholderId = generateUniqueId();
              chatSegments.value.push({
                id: placeholderId,
                type: 'email_placeholder',
                sender: 'ai',
                isGenerating: true,
                timestamp: new Date(),
              });
              currentEmailPlaceholderId.value = placeholderId;
              streamingEmailContent.value = ''; // Clear any previous streaming content
            } else if (toolStart.tag === 'image') {
              // For image tool, we don't need a placeholder as we'll create the image segment directly when we get the content
              console.log('Image tool start detected');
            } else if (toolStart.tag === 'multiimage') {
              // For multiimage tool, we don't need a placeholder as we'll create the multiimage segment directly when we get the content
              console.log('Multiimage tool start detected');
            } else if (toolStart.tag === 'memory') {
              console.log('Memory tool start detected');
            }
            // Ignore other tool types if necessary
            break;

          case 'tool_content':
            const toolContent = segment as ToolContentSegment;
            if (
              toolContent.tag === 'brief' &&
              currentBriefPlaceholderId.value
            ) {
              // Important: Preserve the original content exactly as received
              // Do not modify or clean it here to ensure end tags are detected properly
              streamingBriefContent.value += toolContent.content;

              // --- New: accumulate and emit raw streaming JSON ---
              rawStreamingBrief.value += toolContent.content;
              emit('update:raw-brief-stream', rawStreamingBrief.value);

              // Log for debugging
              if (toolContent.content.includes('</brief>')) {
                console.log('End brief tag detected in tool_content');
              }
            } else if (
              toolContent.tag === 'email' &&
              currentEmailPlaceholderId.value
            ) {
              // Important: Preserve the original content exactly as received
              // Do not modify or clean it here to ensure end tags are detected properly
              streamingEmailContent.value += toolContent.content;

              // --- Accumulate and emit raw streaming JSON ---
              rawStreamingEmail.value += toolContent.content;
              emit('update:raw-email-stream', rawStreamingEmail.value);

              // Log for debugging
              if (toolContent.content.includes('</email>')) {
                console.log('End email tag detected in tool_content');
              }
            }
            // Ignore other tool types
            break;

          case 'tool_end':
            const toolEnd = segment as ToolEndSegment;
            if (toolEnd.tag === 'image') {
              // Process the image URL from the tool content
              let rawContent = toolEnd.rawContent?.trim() ?? '';
              let imageUrl = '';
              let imageName = 'Generated Image'; // Default name

              // Try to parse as JSON first
              try {
                if (rawContent.startsWith('{') && rawContent.endsWith('}')) {
                  const imageData = JSON.parse(rawContent);
                  if (imageData.url) {
                    imageUrl = imageData.url;
                    imageName = imageData.name || 'Generated Image';
                    console.log('Extracted image data from JSON:', {
                      name: imageName,
                      url: imageUrl,
                    });
                  }
                } else {
                  // Legacy format - extract URL using regex
                  const urlRegex = /(https?:\/\/[^\s<>"']+)/;
                  const urlMatch = rawContent.match(urlRegex);
                  if (urlMatch && urlMatch[1]) {
                    imageUrl = urlMatch[1];
                    console.log('Extracted image URL using regex:', imageUrl);
                  }
                }
              } catch (jsonError) {
                console.warn(
                  'Failed to parse image JSON in tool_end:',
                  jsonError,
                );
                // Fall back to regex extraction
                const urlRegex = /(https?:\/\/[^\s<>"']+)/;
                const urlMatch = rawContent.match(urlRegex);
                if (urlMatch && urlMatch[1]) {
                  imageUrl = urlMatch[1];
                  console.log(
                    'Fallback: Extracted image URL using regex:',
                    imageUrl,
                  );
                }
              }

              if (imageUrl) {
                // Add a new image segment to the chat
                chatSegments.value.push({
                  id: generateUniqueId(),
                  type: 'image',
                  sender: 'ai',
                  imageUrl: imageUrl,
                  content: imageName, // Store the image name
                  timestamp: new Date(),
                  imageLoaded: false, // Start with image not loaded
                });
              } else {
                console.error(
                  'Failed to extract valid image URL from:',
                  rawContent,
                );
                // Add error message
                chatSegments.value.push({
                  id: generateUniqueId(),
                  type: 'text',
                  sender: 'ai',
                  content: 'Error: Failed to extract valid image URL.',
                  timestamp: new Date(),
                });
              }
            } else if (toolEnd.tag === 'multiimage') {
              // Process the multiimage JSON from the tool content
              const multiimageContent = toolEnd.rawContent?.trim() ?? '';
              if (multiimageContent) {
                console.log(
                  'Multiimage tool end detected with content:',
                  multiimageContent.substring(0, 100) + '...',
                );
                try {
                  // Parse the JSON array of images
                  const imagesData = JSON.parse(multiimageContent);
                  if (Array.isArray(imagesData) && imagesData.length > 0) {
                    // Add a new multiimage segment to the chat
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'multiimage',
                      sender: 'ai',
                      timestamp: new Date(),
                      images: imagesData.map(img => ({
                        url: img.url,
                        name: img.name || '',
                        loaded: false, // Start with images not loaded
                      })),
                    });
                  } else {
                    console.error(
                      'Invalid multiimage data format:',
                      imagesData,
                    );
                    // Add error message
                    chatSegments.value.push({
                      id: generateUniqueId(),
                      type: 'text',
                      sender: 'ai',
                      content: 'Error: Invalid multiimage data format.',
                      timestamp: new Date(),
                    });
                  }
                } catch (error) {
                  console.error('Failed to parse multiimage JSON:', error);
                  // Add error message
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'text',
                    sender: 'ai',
                    content: 'Error: Failed to parse multiimage data.',
                    timestamp: new Date(),
                  });
                }
              }
            } else if (toolEnd.tag === 'memory') {
              const memContent = toolEnd.rawContent?.trim() ?? '';
              if (memContent) {
                try {
                  const memory = JSON.parse(memContent);
                  console.log('Memory tool end detected with content:', memory);
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'memory',
                    sender: 'ai',
                    timestamp: new Date(),
                    memory,
                  });
                } catch (error) {
                  console.error('Failed to parse memory JSON:', error);
                  chatSegments.value.push({
                    id: generateUniqueId(),
                    type: 'text',
                    sender: 'ai',
                    content: 'Error: Failed to parse memory data.',
                    timestamp: new Date(),
                  });
                }
              }
            } else if (
              toolEnd.tag === 'brief' &&
              currentBriefPlaceholderId.value
            ) {
              console.log(
                'Brief tool_end event received with raw content:',
                toolEnd.rawContent
                  ? toolEnd.rawContent.substring(0, 100) + '...'
                  : 'No raw content available',
              );
              emit('brief-stream-complete'); // Emit completion signal

              const placeholderIndex = chatSegments.value.findIndex(
                s => s.id === currentBriefPlaceholderId.value,
              );
              if (placeholderIndex !== -1) {
                // --- Enhanced Robust Fix ---
                console.log(
                  'Brief tool_end: Processing accumulated content:',
                  streamingBriefContent.value.substring(0, 100) + '...',
                ); // Log first 100 chars

                let extractedJson = ''; // Initialize as empty string
                let parseError = false;
                // Default error message is set directly in the error case

                interface Brief {
                  subjectLine: string;
                  previewText: string;
                  briefText: string;
                }

                // Try multiple approaches to extract the JSON content

                // First, check if we have the raw content from the tool_end event
                let contentToProcess = '';

                if (toolEnd.rawContent && toolEnd.rawContent.trim()) {
                  // Use the raw content from the tool_end event if available
                  contentToProcess = toolEnd.rawContent;
                  console.log('Using raw content from tool_end event');
                } else {
                  // Fall back to the accumulated content
                  contentToProcess = streamingBriefContent.value;
                  console.log('Using accumulated content from streaming');
                }

                // Clean up the content by removing any leading colons or other non-tag characters
                const cleanedContent = contentToProcess.replace(
                  /^[^<]*(<brief>)/i,
                  '$1',
                );
                console.log(
                  'Cleaned content starts with:',
                  cleanedContent.substring(0, 50) + '...',
                );

                // Approach 1: Try to extract content between <brief> tags with a more robust regex
                // This regex handles potential whitespace and newlines around and within the tags
                const tagRegex = /<brief>\s*([\s\S]*?)\s*<\/brief>/i; // Added 'i' flag for case insensitivity
                const match = cleanedContent.match(tagRegex);
                console.log('Tag regex match:', match ? 'Found' : 'Not found');

                // Approach 2: Look for JSON object pattern in the entire content
                // This helps when the AI might not properly wrap the content in tags
                // This regex is more permissive to handle nested objects and arrays
                const jsonRegex =
                  /\{[\s\S]*?"subjectLine"[\s\S]*?"previewText"[\s\S]*?"briefText"[\s\S]*?\}/g;
                const jsonMatches = cleanedContent.match(jsonRegex);
                console.log(
                  'JSON regex matches:',
                  jsonMatches ? jsonMatches.length : 'None',
                );

                // Try to extract JSON directly from the content
                let directJsonMatch: string | null = null;
                try {
                  // Check if the entire content (after removing any non-JSON prefix) is valid JSON
                  const jsonStartIndex = cleanedContent.indexOf('{');
                  const jsonEndIndex = cleanedContent.lastIndexOf('}');

                  console.log(
                    `Direct JSON extraction - start index: ${jsonStartIndex}, end index: ${jsonEndIndex}`,
                  );

                  if (
                    jsonStartIndex !== -1 &&
                    jsonEndIndex !== -1 &&
                    jsonEndIndex > jsonStartIndex
                  ) {
                    directJsonMatch = cleanedContent.substring(
                      jsonStartIndex,
                      jsonEndIndex + 1,
                    );
                    console.log(
                      `Extracted direct JSON of length ${directJsonMatch.length}`,
                    );
                    console.log(
                      `Direct JSON starts with: ${directJsonMatch.substring(
                        0,
                        50,
                      )}...`,
                    );
                  } else {
                    console.log('Could not find valid JSON start/end markers');
                  }
                } catch (e) {
                  console.log('Failed to extract direct JSON', e);
                  directJsonMatch = null;
                }

                if (match && match[1] && match[1].trim()) {
                  // Found content between <brief> tags
                  extractedJson = match[1].trim();
                  console.log(
                    'Brief tool_end: Successfully extracted content from between <brief> tags',
                  );
                } else if (jsonMatches && jsonMatches.length > 0) {
                  // Found JSON-like pattern in the content
                  // Use the largest match as it's likely the complete JSON
                  extractedJson = jsonMatches.reduce(
                    (longest, current) =>
                      current.length > longest.length ? current : longest,
                    '',
                  );
                  console.log(
                    'Brief tool_end: Using JSON pattern match as fallback',
                  );
                } else if (directJsonMatch) {
                  // Use the direct JSON extraction
                  extractedJson = directJsonMatch;
                  console.log(
                    'Brief tool_end: Using direct JSON extraction as fallback',
                  );
                } else {
                  // Fallback: Check if the entire trimmed content itself looks like JSON
                  const trimmedContent = streamingBriefContent.value.trim();
                  if (
                    trimmedContent.startsWith('{') &&
                    trimmedContent.endsWith('}')
                  ) {
                    extractedJson = trimmedContent;
                    console.log(
                      'Brief tool_end: Using entire trimmed content as potential JSON',
                    );
                  } else {
                    console.error(
                      'Failed to extract valid JSON content using any method. Raw content sample:',
                      streamingBriefContent.value.substring(0, 200) + '...',
                    );
                    parseError = true;
                  }
                }

                // --- Enhanced parsing with multiple fallback strategies ---
                let parsedBrief: Brief | null = null;
                if (!parseError && extractedJson) {
                  console.log(
                    'Attempting to parse extracted JSON of length',
                    extractedJson.length,
                  );
                  console.log(
                    'JSON to parse starts with:',
                    extractedJson.substring(0, 100),
                  );

                  try {
                    // First attempt: Try direct JSON parsing
                    try {
                      parsedBrief = JSON.parse(extractedJson) as Brief;
                      console.log(
                        'Brief tool_end: Successfully parsed JSON directly',
                      );
                    } catch (directParseError) {
                      // Log the error
                      console.log(
                        'Direct JSON parsing failed:',
                        directParseError,
                      );

                      // Second attempt: Try with jsonrepair
                      console.log(
                        'Brief tool_end: Direct parsing failed, trying jsonrepair',
                      );
                      const repairedJson = jsonrepair(extractedJson);
                      console.log(
                        'Repaired JSON:',
                        repairedJson.substring(0, 100),
                      );
                      parsedBrief = JSON.parse(repairedJson) as Brief;
                      console.log(
                        'Brief tool_end: Successfully parsed JSON after repair',
                      );
                    }

                    // Validate expected fields with type checking
                    if (
                      !parsedBrief ||
                      typeof parsedBrief.subjectLine !== 'string' ||
                      typeof parsedBrief.previewText !== 'string' ||
                      typeof parsedBrief.briefText !== 'string'
                    ) {
                      console.error(
                        'Parsed brief JSON missing required keys or has wrong types:',
                        parsedBrief,
                      );

                      // Try to recover by creating a valid object from whatever we have
                      if (parsedBrief) {
                        // Create a valid brief with defaults for missing fields
                        parsedBrief = {
                          subjectLine:
                            typeof parsedBrief.subjectLine === 'string'
                              ? parsedBrief.subjectLine
                              : 'Default Subject',
                          previewText:
                            typeof parsedBrief.previewText === 'string'
                              ? parsedBrief.previewText
                              : 'Default Preview',
                          briefText:
                            typeof parsedBrief.briefText === 'string'
                              ? parsedBrief.briefText
                              : 'Default Brief Content',
                        };
                        console.log(
                          'Created valid brief object with defaults for missing fields',
                        );
                      } else {
                        throw new Error(
                          'Parsed brief JSON missing required keys or has wrong types.',
                        );
                      }
                    }
                  } catch (e) {
                    // Log the content that failed parsing and the original accumulated content
                    console.error('Failed to parse brief JSON:', e);
                    console.error(
                      'Attempted to parse:',
                      extractedJson.substring(0, 200) + '...',
                    );
                    parseError = true;
                    parsedBrief = null; // Ensure null on error
                  }
                } else if (!extractedJson && !parseError) {
                  // This case should ideally be caught by the initial checks, but acts as a safeguard.
                  console.warn('No valid brief JSON content found to parse.');
                  parseError = true; // Treat as an error for UI update
                }

                // --- Finally, update UI based on success or failure ---
                if (!parseError && parsedBrief) {
                  // Create brief data object
                  const briefData = {
                    subjectLine: parsedBrief.subjectLine,
                    previewText: parsedBrief.previewText,
                    briefText: parsedBrief.briefText,
                  };

                  // Log the extracted subject line and preview text
                  console.log('Extracted subject line:', briefData.subjectLine);
                  console.log('Extracted preview text:', briefData.previewText);

                  // Emit the update event with parsed data
                  emit('update:brief', briefData);

                  // Emit auto-save event to trigger immediate saving
                  emit('auto-save-brief', briefData);

                  // Replace placeholder with a brief card instead of just a text message
                  // This makes newly generated briefs consistent with historic briefs
                  chatSegments.value.splice(placeholderIndex, 1, {
                    id: currentBriefPlaceholderId.value!, // Assert non-null as we found the index
                    type: 'historic_brief', // Use the same type as historic briefs for consistency
                    sender: 'ai',
                    content: 'Campaign Brief',
                    timestamp: new Date(),
                    briefData: briefData, // Include the parsed brief data
                    rawContent: extractedJson, // Include the raw JSON
                    isGenerating: false,
                    artifactType: 'brief',
                  });

                  console.log(
                    'Replaced brief placeholder with historic_brief card',
                  );
                } else {
                  // Replace placeholder with error card component
                  chatSegments.value.splice(placeholderIndex, 1, {
                    id: currentBriefPlaceholderId.value!, // Assert non-null
                    type: 'brief_error',
                    sender: 'ai',
                    errorMessage: 'AI generated brief in an invalid format',
                    errorDetails:
                      'This sometimes happens with AI, we are working to minimize this as much as possible. Please try again!',
                    timestamp: new Date(),
                  });
                }
              } else {
                console.error(
                  'Brief tool end received, but placeholder not found:',
                  currentBriefPlaceholderId.value,
                );
              }

              // Reset brief state regardless of success/failure
              currentBriefPlaceholderId.value = null;
              // streamingBriefContent.value = ''; // No longer the primary source for final content
              rawStreamingBrief.value = ''; // Reset the raw accumulator
            } else if (
              toolEnd.tag === 'email' &&
              currentEmailPlaceholderId.value
            ) {
              console.log(
                'Email tool_end event received with raw content:',
                (toolEnd.rawContent?.substring(0, 100) || '') + '...',
              );
              emit('email-stream-complete'); // Emit completion signal

              const placeholderIndex = chatSegments.value.findIndex(
                s => s.id === currentEmailPlaceholderId.value,
              );
              if (placeholderIndex !== -1) {
                // Process the email JSON content
                console.log(
                  'Email tool_end: Processing accumulated content:',
                  streamingEmailContent.value.substring(0, 100) + '...',
                );

                let extractedJson = ''; // Initialize as empty string
                let parseError = false;
                let errorMessage = 'Error: Failed to process email update.';

                // Try to extract the JSON content
                let contentToProcess = '';

                if (toolEnd.rawContent && toolEnd.rawContent.trim()) {
                  // Use the raw content from the tool_end event if available
                  contentToProcess = toolEnd.rawContent;
                  console.log('Using raw content from tool_end event');
                } else {
                  // Fall back to the accumulated content
                  contentToProcess = streamingEmailContent.value;
                  console.log('Using accumulated content from streaming');
                }

                // Clean up the content by removing any leading non-tag characters
                const cleanedContent = contentToProcess.replace(
                  /^[^<]*(<email>)/i,
                  '$1',
                );
                console.log(
                  'Cleaned email content starts with:',
                  cleanedContent.substring(0, 50) + '...',
                );

                // Extract content between <email> tags
                const tagRegex = /<email>\s*([\s\S]*?)\s*<\/email>/i;
                const match = cleanedContent.match(tagRegex);
                console.log(
                  'Email tag regex match:',
                  match ? 'Found' : 'Not found',
                );

                if (match && match[1] && match[1].trim()) {
                  // Found content between <email> tags
                  extractedJson = match[1].trim();
                  console.log(
                    'Email tool_end: Successfully extracted content from between <email> tags',
                  );
                } else {
                  // Try to find JSON directly
                  const jsonStartIndex = cleanedContent.indexOf('{');
                  const jsonEndIndex = cleanedContent.lastIndexOf('}');

                  if (
                    jsonStartIndex !== -1 &&
                    jsonEndIndex !== -1 &&
                    jsonEndIndex > jsonStartIndex
                  ) {
                    extractedJson = cleanedContent.substring(
                      jsonStartIndex,
                      jsonEndIndex + 1,
                    );
                    console.log(
                      'Email tool_end: Using direct JSON extraction as fallback',
                    );
                  } else {
                    console.error(
                      'Failed to extract valid JSON content for email using any method',
                    );
                    parseError = true;
                    errorMessage =
                      'Error: Could not identify valid JSON in the email content.';
                  }
                }

                // Parse the extracted JSON
                let parsedEmail = null;
                if (!parseError && extractedJson) {
                  try {
                    // Try direct JSON parsing
                    parsedEmail = JSON.parse(extractedJson);
                    console.log(
                      'Email tool_end: Successfully parsed JSON directly',
                    );
                  } catch (directParseError) {
                    console.log(
                      'Direct JSON parsing failed:',
                      directParseError,
                    );
                    try {
                      // Try with jsonrepair
                      const repairedJson = jsonrepair(extractedJson);
                      parsedEmail = JSON.parse(repairedJson);
                      console.log(
                        'Email tool_end: Successfully parsed JSON after repair',
                      );
                    } catch (repairError) {
                      console.error('Failed to parse email JSON:', repairError);
                      parseError = true;
                      errorMessage = 'Error: Failed to parse email data.';
                    }
                  }
                }

                // Update UI based on success or failure
                if (!parseError && parsedEmail) {
                  // Check if we need to generate the final email design
                  if (
                    parsedEmail &&
                    typeof parsedEmail === 'object' &&
                    'components' in parsedEmail &&
                    Array.isArray(
                      (parsedEmail as {components: unknown[]}).components,
                    )
                  ) {
                    console.log(
                      'Email contains component list, generating final email design',
                    );
                    // Convert the components to JSON string for the API call
                    const componentJSONText = JSON.stringify(parsedEmail);

                    // Replace placeholder with an email card for components
                    chatSegments.value.splice(placeholderIndex, 1, {
                      id: currentEmailPlaceholderId.value!,
                      type: 'historic_email',
                      sender: 'ai',
                      content: 'Email Design',
                      timestamp: new Date(),
                      design: parsedEmail,
                      rawContent: extractedJson,
                      isGenerating: false,
                      artifactType: 'email',
                    });

                    console.log(
                      'Replaced email placeholder with historic_email card (components)',
                    );

                    // Emit the special event to generate the email from components
                    emit('generate-email-from-components', componentJSONText);
                  } else {
                    // This is already a complete email design, emit it directly
                    // Emit the update event with parsed data
                    emit('update:email', parsedEmail);

                    // Emit auto-save event to trigger immediate saving
                    emit('auto-save-email', parsedEmail);

                    // Replace placeholder with an email card
                    chatSegments.value.splice(placeholderIndex, 1, {
                      id: currentEmailPlaceholderId.value!,
                      type: 'historic_email',
                      sender: 'ai',
                      content: 'Email Design',
                      timestamp: new Date(),
                      design: parsedEmail,
                      rawContent: extractedJson,
                      isGenerating: false,
                      artifactType: 'email',
                    });

                    console.log(
                      'Replaced email placeholder with historic_email card',
                    );
                  }
                } else {
                  // Replace placeholder with error card component
                  chatSegments.value.splice(placeholderIndex, 1, {
                    id: currentEmailPlaceholderId.value!,
                    type: 'email_error',
                    sender: 'ai',
                    errorMessage: 'Failed to parse email JSON',
                    errorDetails: errorMessage, // Use the determined error message as details
                    timestamp: new Date(),
                  });
                }
              } else {
                console.error(
                  'Email tool end received, but placeholder not found:',
                  currentEmailPlaceholderId.value,
                );
              }

              // Reset email state
              currentEmailPlaceholderId.value = null;
              rawStreamingEmail.value = '';
            }
            // Ignore other tool types
            break;
        }
      }
    };

    const handleStartConversation = async (initialMessage: string) => {
      // Add user message visually
      chatSegments.value.push({
        id: generateUniqueId(),
        type: 'text',
        sender: 'user',
        content: initialMessage,
        timestamp: new Date(),
      });
      scrollToBottom(); // Always scroll to bottom after user sends a message

      customerIOTrackEvent('Started Campaign Chat');

      console.log('handleStartConversation called', props.campaignId);
      // Prepare payload
      const payload = {
        message: initialMessage,
        promptTemplateId: 12,
        campaignId: props.campaignId,
        context: {briefText: props.currentBriefText},
      };

      // Reset parser and state before API call
      messageParserService.reset();
      currentBriefPlaceholderId.value = null;
      streamingBriefContent.value = '';
      isWaitingForAI.value = true; // Set waiting state

      await handleApiStream(startConversation(payload));
      await quotaService.refreshQuota();
    };

    // Track if this is the first message after loading the conversation
    const isFirstMessageAfterLoad = ref(true);

    const handlePostMessage = async (
      convId: string,
      message: string,
      updateSystemPrompt = false,
    ) => {
      // Add user message visually
      chatSegments.value.push({
        id: generateUniqueId(),
        type: 'text',
        sender: 'user',
        content: message,
        timestamp: new Date(),
      });
      scrollToBottom(); // Always scroll to bottom after user sends a message

      // For the first message after loading a conversation, update the system prompt
      // to ensure the AI has the latest functionality
      const shouldUpdateSystemPrompt =
        isFirstMessageAfterLoad.value || updateSystemPrompt;

      // After the first message, set the flag to false
      if (isFirstMessageAfterLoad.value) {
        isFirstMessageAfterLoad.value = false;
      }

      // Prepare payload
      const payload = {
        message: message,
        promptTemplateId: 12, // Specific prompt template ID
        context: {briefText: props.currentBriefText}, // Include current brief text
        updateSystemPrompt: shouldUpdateSystemPrompt, // Update on first message or when specifically requested
      };

      // Reset parser and state before API call
      messageParserService.reset();
      currentBriefPlaceholderId.value = null;
      streamingBriefContent.value = '';
      isWaitingForAI.value = true; // Set waiting state

      if (shouldUpdateSystemPrompt) {
        console.log('Posting message with updateSystemPrompt=true');
      }
      await handleApiStream(postMessage(convId, payload, true, true, true));
      await quotaService.refreshQuota();
    };

    // Modified to accept an external message parameter
    const sendMessage = (externalMessage?: string) => {
      if (!quotaService.canSendMessage.value) {
        showQuotaModal.value = true;
        return;
      }
      // Use external message if provided, otherwise use the input field
      const text = externalMessage
        ? externalMessage.trim()
        : newMessage.value.trim();
      // Don't send empty messages or if already waiting for AI response
      if (!text || isWaitingForAI.value) {
        // Re-focus the chat input if it exists
        if (messageTextarea.value) {
          nextTick(() => {
            if (messageTextarea.value) messageTextarea.value.focus();
          });
        }
        return;
      }

      const currentConvId = conversationId.value; // Capture value before async operations

      customerIOTrackEvent('Sent Campaign Chat Message');

      // Only clear the input field if we're using it (not an external message)
      if (!externalMessage) {
        newMessage.value = ''; // Clear input immediately

        // Reset textarea height after clearing
        if (messageTextarea.value) {
          messageTextarea.value.style.height = 'auto';
        }
      }

      // Update quota immediately when sending message
      quotaService.addMessage(1);
      
      if (!currentConvId) {
        handleStartConversation(text);
      } else {
        // No need to pass updateSystemPrompt here since handlePostMessage
        // will check isFirstMessageAfterLoad and update if needed
        handlePostMessage(currentConvId, text, false);
      }
      // Scroll is handled within handleStart/Post after adding user message
    };

    // Method to resize the textarea as user types
    const resizeMessageTextarea = () => {
      if (messageTextarea.value) {
        // Reset height to auto to properly calculate the new height
        messageTextarea.value.style.height = 'auto';

        // Set the height based on scrollHeight (content height)
        // with a maximum of 4 lines (approximately 96px)
        const newHeight = Math.min(messageTextarea.value.scrollHeight, 96);
        messageTextarea.value.style.height = `${newHeight}px`;
      }
    };

    // Initialize textarea when component is mounted
    onMounted(async () => {
      // Set initial height for textarea
      if (messageTextarea.value) {
        messageTextarea.value.style.height = 'auto';
      }
      await quotaService.initializeQuota();
    });

    // Load conversation if conversationId is provided

    const loadConversation = async (convId: string | number) => {
      console.log('BriefChat: loadConversation called with ID:', convId);
      isWaitingForAI.value = true;
      try {
        const conversation = await getConversation(convId.toString());
        if (!conversation) throw new Error('Failed to load conversation');
        conversationId.value = conversation.id.toString();
        isFirstMessageAfterLoad.value = true;
        chatSegments.value = [];
        if (conversation.messages && conversation.messages.length > 0) {
          const sorted = [...conversation.messages].sort((a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
          );
          for (const msg of sorted) {
            if (msg.role === 'user') {
              const processed = typeof msg.content === 'string'
                ? msg.content.replace(/^[\"'](.*)[\"']$/g, '$1')
                : msg.content;
              chatSegments.value.push({
                id: generateUniqueId(),
                type: 'text',
                sender: 'user',
                content: processed,
                timestamp: new Date(msg.createdAt),
              });
            } else if (msg.role === 'assistant') {
              const {segments} = messageArtifactService.processAssistantMessage(
                msg,
                generateUniqueId,
              );
              for (const seg of segments) {
                if (seg.type === 'brief_artifact' && seg.rawContent) {
                  try {
                    const parsed = JSON.parse(jsonrepair(seg.rawContent));
                    const briefData = {
                      subjectLine: parsed.subjectLine || '',
                      previewText: parsed.previewText || '',
                      briefText: Utils.cleanMarkdown(parsed.briefText || ''),
                    };
                    chatSegments.value.push({
                      id: seg.id,
                      type: 'historic_brief',
                      sender: 'ai',
                      content: 'Campaign Brief',
                      timestamp: new Date(msg.createdAt),
                      briefData,
                      rawContent: seg.rawContent,
                      isGenerating: false,
                      artifactType: 'brief',
                    });
                  } catch (e) {
                    chatSegments.value.push({
                      id: seg.id,
                      type: 'brief_error',
                      sender: 'ai',
                      errorMessage: 'Failed to parse brief JSON',
                      errorDetails:
                        'This sometimes happens with AI, we are working to minimize this as much as possible. Please try again!',
                      timestamp: new Date(msg.createdAt),
                    });
                  }
                } else if (seg.type === 'email_artifact' && seg.rawContent) {
                  try {
                    const emailJson = JSON.parse(jsonrepair(seg.rawContent));
                    chatSegments.value.push({
                      id: seg.id,
                      type: 'historic_email',
                      sender: 'ai',
                      content: 'Email Design',
                      timestamp: new Date(msg.createdAt),
                      design: emailJson,
                      rawContent: seg.rawContent,
                      isGenerating: false,
                      artifactType: 'email',
                    });
                  } catch (err) {
                    chatSegments.value.push({
                      id: seg.id,
                      type: 'email_error',
                      sender: 'ai',
                      errorMessage: 'Failed to parse email JSON',
                      errorDetails: (err as Error).message || '',
                      timestamp: new Date(msg.createdAt),
                    });
                  }
                } else {
                  chatSegments.value.push(seg as any);
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error loading conversation:', error);
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'brief_error',
          sender: 'ai',
          errorMessage: 'Failed to load conversation',
          errorDetails: 'The conversation could not be loaded. Please try again.',
          timestamp: new Date(),
        });
      } finally {
        isWaitingForAI.value = false;
        nextTick(() => {
          scrollToBottom();
        });
      }
    };
    // Function to load a brief when clicked (works for both historic and newly generated briefs)
    const loadHistoricBrief = (segment: ChatMessageSegment) => {
      console.log(
        'Loading brief:',
        segment.id,
        segment.briefData ? 'Data present' : 'Data MISSING',
      );

      // Only show confirmation if the brief has actually been edited
      // Get the original brief text from the segment that's going to be loaded
      const originalBriefText = segment.briefData?.briefText || '';
      const currentBriefText = props.currentBriefText || '';

      // Compare the current brief with the original brief
      // Only show confirmation if they're different and current brief isn't empty
      if (
        currentBriefText.trim() !== '' &&
        currentBriefText !== originalBriefText
      ) {
        // Store the segment to be loaded after confirmation
        pendingBriefSegment.value = segment;
        // Show confirmation modal
        showConfirmModal.value = true;
        return;
      }

      // No unsaved changes, proceed with loading
      applyHistoricBrief(segment);
    };

    // Function to apply the selected brief after confirmation or directly
    const applyHistoricBrief = (segment: ChatMessageSegment) => {
      if (segment.briefData) {
        // Create the brief data object with default values if fields are missing
        const briefData = {
          subjectLine: segment.briefData.subjectLine || '',
          previewText: segment.briefData.previewText || '',
          briefText: Utils.cleanMarkdown(segment.briefData.briefText || ''),
        };

        // Log the subject line and preview text being emitted
        console.log('Emitting subject line:', briefData.subjectLine);
        console.log('Emitting preview text:', briefData.previewText);
        console.log('Emitting brief text length:', briefData.briefText.length);

        // Emit the brief data to update the Campaign Brief area
        emit('update:brief', briefData);

        // Emit auto-save event to trigger immediate saving
        emit('auto-save-brief', briefData);

        // Reset visual indicators on all brief cards
        document.querySelectorAll('.historic_brief').forEach(card => {
          const htmlCard = card as HTMLElement;
          htmlCard.classList.remove('border-green-300', 'bg-green-50');
          htmlCard.classList.add('border-purple-200');

          const statusSpan = htmlCard.querySelector('.brief-status');
          if (statusSpan) {
            // Determine if this is a recent brief or a historic one
            const cardId = htmlCard.getAttribute('data-segment-id');
            const cardSegment = chatSegments.value.find(s => s.id === cardId);
            const isRecent =
              cardSegment?.timestamp &&
              new Date().getTime() - new Date(cardSegment.timestamp).getTime() <
                60000;

            statusSpan.textContent = isRecent
              ? 'Generated just now'
              : 'From historic chat';
            statusSpan.classList.remove('text-green-600');
            statusSpan.classList.add('text-purple-600');
          }
        });

        // Add a visual indicator to the clicked brief card
        const briefCard = document.querySelector(
          `[data-segment-id="${segment.id}"]`,
        ) as HTMLElement | null;
        if (briefCard) {
          briefCard.classList.add('border-green-300', 'bg-green-50');
          briefCard.classList.remove('border-purple-200');

          // Add a checkmark icon or text to indicate it was loaded
          const statusSpan = briefCard.querySelector('.brief-status');
          if (statusSpan) {
            statusSpan.textContent = 'Loaded ✓';
            statusSpan.classList.remove('text-purple-600');
            statusSpan.classList.add('text-green-600');
          }
        }
      } else {
        console.error(
          'Attempted to load historic brief but briefData was missing on segment:',
          segment.id,
        );

        // Show an error message to the user
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'brief_error',
          sender: 'ai',
          errorMessage: 'Failed to load brief',
          errorDetails:
            'The brief data is missing or corrupted. Please try generating a new brief.',
          timestamp: new Date(),
        });

        scrollToBottom();
      }
    }; // End of loadHistoricBrief

    // Function to load an email design when clicked
    const loadHistoricEmail = (segment: ChatMessageSegment) => {
      console.log(
        'Loading email design:',
        segment.id,
        segment.design ? 'Design present' : 'Design MISSING',
      );
      if (segment.design) {
        // Check if this is a component list that needs to be processed
        if (
          segment.design &&
          typeof segment.design === 'object' &&
          'components' in segment.design &&
          Array.isArray(segment.design.components)
        ) {
          console.log(
            'Email contains component list, emitting raw JSON for API processing',
          );

          // Convert the design to JSON string
          const componentJSONText = JSON.stringify(segment.design);

          // Emit a special event to trigger immediate API call
          emit('generate-email-from-components', componentJSONText);

          // No need to add a message - we'll just use the existing email card
          // and the ContentTask component will handle showing the loading state
        } else {
          // This is already a complete email design, emit it directly
          // Emit the email design data to update the parent component
          emit('update:email', segment.design);

          // Emit auto-save event to trigger immediate saving
          emit('auto-save-email', segment.design);
        }

        // Reset visual indicators on all email cards
        document.querySelectorAll('.historic_email').forEach(card => {
          const htmlCard = card as HTMLElement;
          htmlCard.classList.remove('border-green-300', 'bg-green-50');
          htmlCard.classList.add('border-purple-200');

          const statusSpan = htmlCard.querySelector('.email-status');
          if (statusSpan) {
            // Determine if this is a recent email or a historic one
            const cardId = htmlCard.getAttribute('data-segment-id');
            const cardSegment = chatSegments.value.find(s => s.id === cardId);
            const isRecent =
              cardSegment?.timestamp &&
              new Date().getTime() - new Date(cardSegment.timestamp).getTime() <
                60000;

            statusSpan.textContent = isRecent
              ? 'Generated just now'
              : 'From historic chat';
            statusSpan.classList.remove('text-green-600');
            statusSpan.classList.add('text-purple-600');
          }
        });

        // Add a visual indicator to the clicked email card
        const emailCard = document.querySelector(
          `[data-segment-id="${segment.id}"]`,
        ) as HTMLElement | null;
        if (emailCard) {
          emailCard.classList.add('border-green-300', 'bg-green-50');
          emailCard.classList.remove('border-purple-200');

          // Add a checkmark icon or text to indicate it was loaded
          const statusSpan = emailCard.querySelector('.email-status');
          if (statusSpan) {
            statusSpan.textContent = 'Loaded ✓';
            statusSpan.classList.remove('text-purple-600');
            statusSpan.classList.add('text-green-600');
          }
        }
      } else {
        console.error(
          'Attempted to load historic email but design was missing on segment:',
          segment.id,
        );

        // Show an error message to the user
        chatSegments.value.push({
          id: generateUniqueId(),
          type: 'email_error',
          sender: 'ai',
          errorMessage: 'Failed to load email design',
          errorDetails:
            'The email design data is missing or corrupted. Please try generating a new email design.',
          timestamp: new Date(),
        });

        scrollToBottom();
      }
    }; // End of loadHistoricEmail

    // Watch for changes to newMessage and resize textarea accordingly
    watch(newMessage, () => {
      nextTick(() => {
        resizeMessageTextarea();
      });
    });

    // Ensure chat is scrolled to bottom when first loaded
    onMounted(() => {
      nextTick(() => {
        scrollToBottom();
      });
    });

    // Watch for changes to the conversationId prop
    watch(
      () => props.conversationId,
      (newVal, oldVal) => {
        // Only load if the ID actually changes to a non-null value
        // and is different from the current internal conversation ID
        console.log('BriefChat: conversationId prop changed.', {
          newVal,
          oldVal,
          internalId: conversationId.value,
        });
        if (newVal && newVal !== conversationId.value) {
          // Make sure we have a valid conversationId
          if (
            typeof newVal === 'number' ||
            (typeof newVal === 'string' && newVal.trim() !== '')
          ) {
            loadConversation(newVal);
          } else {
            console.warn('BriefChat: Invalid conversationId prop:', newVal);
            // Clear chat if the prop becomes invalid/null
            chatSegments.value = [];
            conversationId.value = null;
            isWaitingForAI.value = false;
          }
        } else if (!newVal && conversationId.value) {
          // Handle case where prop becomes null/undefined after being set
          console.log(
            'BriefChat: conversationId prop became null/undefined. Clearing chat.',
          );
          chatSegments.value = [];
          conversationId.value = null;
          isWaitingForAI.value = false;
        }
      },
      {immediate: true},
    ); // immediate: true ensures loadConversation runs on initial mount if prop is set

    // Watch for changes to the forceReload prop
    watch(
      () => props.forceReload,
      newVal => {
        console.log('BriefChat: forceReload prop changed to:', newVal);
        if (newVal && props.conversationId) {
          // Force reload the conversation
          console.log(
            'BriefChat: Force reloading conversation with ID:',
            props.conversationId,
          );
          loadConversation(props.conversationId);
        }
      },
    );

    // Handle Enter key: Shift+Enter for newline, Enter to send
    const handleEnterKey = (event: KeyboardEvent) => {
      if (event.shiftKey) {
        // Allow default (insert newline)
        return;
      }

      // Prevent default (no newline)
      event.preventDefault();

      // Only send message if not waiting for AI response
      if (!isWaitingForAI.value) {
        sendMessage();
      } else {
        // Ensure chat input stays focused
        if (messageTextarea.value) {
          nextTick(() => {
            if (messageTextarea.value) messageTextarea.value.focus();
          });
        }
      }
    };

    // Handle when a prompt is selected
    const handlePromptSelected = (promptText: string) => {
      newMessage.value = promptText;

      // Use the sendMessage function to handle the message
      // This will use the same logic for updating the system prompt
      if (!isWaitingForAI.value) {
        sendMessage();
      } else if (messageTextarea.value) {
        // Keep focus on textarea if we can't send
        nextTick(() => {
          if (messageTextarea.value) messageTextarea.value.focus();
        });
      }
    };

    // Handle quick action button clicks
    const handleQuickAction = (promptText: string) => {
      // Use the same logic as handlePromptSelected
      newMessage.value = promptText;
      if (!isWaitingForAI.value) {
        sendMessage();
      } else if (messageTextarea.value) {
        // Keep focus on textarea if we can't send
        nextTick(() => messageTextarea.value?.focus());
      }
    };

    // Public method to send a message from outside components (like AskRaleonPopup)
    const sendExternalMessage = (message: string) => {
      console.log('Sending external message:', message);
      sendMessage(message);
    };

    // Handle retry for brief/email parsing errors
    const handleErrorRetry = (segment: ChatMessageSegment, message: string) => {
      console.log(
        'Handling error retry for segment:',
        segment.id,
        'with message:',
        message,
      );
      // Send the retry message to the AI
      sendMessage(message);
    };

    // Handle opening image in a new tab
    const openImageInNewTab = (url: string) => {
      window.open(url, '_blank');
    };

    // Handle successful image loading
    const handleImageLoad = (segmentId: string) => {
      console.log('Image loaded successfully for segment:', segmentId);
      // Find the segment and mark the image as loaded
      const segmentIndex = chatSegments.value.findIndex(
        s => s.id === segmentId,
      );
      if (segmentIndex !== -1) {
        // Update the segment to mark image as loaded
        const segment = chatSegments.value[segmentIndex];
        chatSegments.value.splice(segmentIndex, 1, {
          ...segment,
          imageLoaded: true,
        });
      }
    };

    // Handle image loading errors
    const handleImageError = (_event: Event, segmentId: string) => {
      console.error('Failed to load image for segment:', segmentId);
      // Find the segment and update it to show an error message
      const segmentIndex = chatSegments.value.findIndex(
        s => s.id === segmentId,
      );
      if (segmentIndex !== -1) {
        // Replace the image segment with an error text segment
        chatSegments.value.splice(segmentIndex, 1, {
          id: segmentId,
          type: 'text',
          sender: 'ai',
          content:
            'Error: Unable to load image. The image URL may be invalid or inaccessible.',
          timestamp: new Date(),
        });
      }
    };

    // Handle multi-image loading - updated to work with the component
    const handleMultiImageLoad = (
      segmentId: string,
      imageIndex: number,
      updatedImages: any[],
    ) => {
      console.log(
        `Multi-image loaded successfully for segment: ${segmentId}, image index: ${imageIndex}`,
      );
      // Find the segment
      const segmentIndex = chatSegments.value.findIndex(
        s => s.id === segmentId,
      );
      if (segmentIndex !== -1) {
        // Update the segment with the new images array
        const updatedSegment = {
          ...chatSegments.value[segmentIndex],
          images: updatedImages,
        };

        // Replace the segment in the array
        chatSegments.value.splice(segmentIndex, 1, updatedSegment);
      }
    };

    // Handle multi-image loading errors - updated to work with the component
    const handleMultiImageError = (
      _event: Event,
      segmentId: string,
      imageIndex: number,
      updatedImages: any[],
    ) => {
      console.error(
        `Failed to load multi-image for segment: ${segmentId}, image index: ${imageIndex}`,
      );
      // Find the segment
      const segmentIndex = chatSegments.value.findIndex(
        s => s.id === segmentId,
      );
      if (segmentIndex !== -1) {
        // Update the segment with the new images array
        const updatedSegment = {
          ...chatSegments.value[segmentIndex],
          images: updatedImages,
        };

        // Replace the segment in the array
        chatSegments.value.splice(segmentIndex, 1, updatedSegment);
      }
    };

    // Confirm modal handlers
    const confirmBriefLoad = () => {
      if (pendingBriefSegment.value) {
        applyHistoricBrief(pendingBriefSegment.value);
        pendingBriefSegment.value = null;
      }
      showConfirmModal.value = false;
    };

    return {
      newMessage,
      chatSegments,
      isWaitingForAI,
      sendMessage,
      sendExternalMessage, // Expose the public method for external components
      generateUniqueId,
      scrollToBottom,
      chatHistoryContainer,
      messageTextarea,
      resizeMessageTextarea,
      loadHistoricBrief,
      loadHistoricEmail,
      handleEnterKey,
      briefPrompts,
      quickActions, // Add quick actions array
      handlePromptSelected,
      handleQuickAction, // Add quick action handler
      isAtBottom,
      handleScroll,
      openImageInNewTab,
      handleImageLoad,
      handleImageError,
      handleMultiImageLoad,
      handleMultiImageError,
      handleErrorRetry, // Add the error retry handler
      // Confirm modal
      showConfirmModal,
      confirmBriefLoad,
      showQuotaModal,
      canSendMessage: quotaService.canSendMessage,
      quota: quotaService.quota,
      quotaService,
      // Text formatting functions
      formatText,
      containsFormatting,
      // Method to set the message directly
      setMessage: (message: string) => {
        newMessage.value = message;
        // Resize the textarea after setting the message
        nextTick(() => {
          if (messageTextarea.value) {
            resizeMessageTextarea();
          }
        });
      },
    };
  },
  methods: {
    decodeBriefText: (s: string) =>
      s.replace(/\\n/g, '\n').replace(/\\"/g, '"'),
  },
});
</script>

<style scoped>
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #b1b2b4 #f3f4f6;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 9999px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: #8e9299;
  border-radius: 9999px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #8e9299;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* autoprefixer: off */
  line-clamp: 2;
  /* autoprefixer: on */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.extra-smooth-gradient-text {
  /* Using more color stops for ultra-smooth transition */
  background-image: linear-gradient(
    90deg,
    #6536e2 0%,
    #6536e2 15%,
    #4c63df 30%,
    #1e90db 50%,
    #4c63df 70%,
    #6536e2 85%,
    #6536e2 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
</style>
